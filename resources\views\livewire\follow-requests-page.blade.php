@php
    use Illuminate\Support\Facades\Storage;
@endphp

<flux:main class="min-h-screen bg-zinc-900">
    <div class="flex-1">
        <div class="max-w-4xl mx-auto p-6">
            <!-- Header -->
            <div class="mb-8">
                <div class="flex items-center gap-3 mb-2">
                    <flux:icon name="user-plus" class="h-8 w-8 text-[#E60073]" />
                    <h1 class="text-3xl font-bold text-gray-300">Solicitações para Seguir</h1>
                </div>
                <p class="text-gray-400">
                    <PERSON><PERSON><PERSON><PERSON> as solicitações de usuários que querem seguir você
                </p>
            </div>

            <!-- Content Card -->
            <div class="bg-zinc-800 border border-zinc-700 rounded-lg shadow-lg">
                <!-- Stats Header -->
                <div class="px-6 py-4 border-b border-zinc-700">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-2">
                            <flux:icon name="bell" class="h-5 w-5 text-[#00FFF7]" />
                            <span class="text-gray-300 font-medium">
                                {{ count($followRequests) }} {{ count($followRequests) === 1 ? 'solicitação pendente' : 'solicitações pendentes' }}
                            </span>
                        </div>
                        @if(count($followRequests) > 0)
                            <flux:badge color="yellow" size="sm">
                                {{ count($followRequests) }}
                            </flux:badge>
                        @endif
                    </div>
                </div>

                <!-- Requests List -->
                @if(count($followRequests) > 0)
                    <div class="divide-y divide-zinc-700">
                        @foreach($followRequests as $request)
                            <div class="px-6 py-4 hover:bg-zinc-700/50 transition-colors">
                                <div class="flex items-center justify-between">
                                    <!-- User Info -->
                                    <div class="flex items-center gap-4">
                                        <div class="relative">
                                            <img src="{{ !empty($request->sender->userPhotos->first()) ? Storage::url($request->sender->userPhotos->first()->photo_path) : asset('images/users/avatar.svg') }}"
                                                 class="w-16 h-16 rounded-full object-cover border-2 border-zinc-600">
                                            <!-- Online Status Indicator -->
                                            <div class="absolute bottom-0 right-0 w-4 h-4 bg-green-500 border-2 border-zinc-800 rounded-full"></div>
                                        </div>
                                        
                                        <div class="flex-1">
                                            <div class="flex items-center gap-2 mb-1">
                                                <a href="/{{ $request->sender->username }}" 
                                                   class="font-semibold text-gray-300 hover:text-[#E60073] transition-colors">
                                                    {{ $request->sender->name }}
                                                </a>
                                                @if($request->sender->role === 'vip')
                                                    <flux:badge color="yellow" size="sm">VIP</flux:badge>
                                                @endif
                                            </div>
                                            <a href="/{{ $request->sender->username }}" 
                                               class="text-gray-400 hover:text-gray-300 text-sm transition-colors">
                                                {{ '@' . $request->sender->username }}
                                            </a>
                                            <p class="text-sm text-gray-500 mt-1">
                                                Solicitou seguir você • {{ \Carbon\Carbon::parse($request->created_at)->diffForHumans() }}
                                            </p>
                                        </div>
                                    </div>

                                    <!-- Action Buttons -->
                                    <div class="flex gap-3">
                                        <flux:button 
                                            wire:click="accept('{{ $request->id }}')"
                                            variant="primary"
                                            size="sm"
                                            class="bg-green-600 hover:bg-green-700 text-white">
                                            <flux:icon name="check" class="h-4 w-4 mr-1" />
                                            Aceitar
                                        </flux:button>
                                        
                                        <flux:button 
                                            wire:click="reject('{{ $request->id }}')"
                                            variant="ghost"
                                            size="sm"
                                            class="text-gray-400 hover:text-gray-300 border-gray-600 hover:border-gray-500">
                                            <flux:icon name="x-mark" class="h-4 w-4 mr-1" />
                                            Rejeitar
                                        </flux:button>
                                        
                                        <flux:button 
                                            onclick="window.open('/{{ $request->sender->username }}', '_blank')"
                                            variant="ghost"
                                            size="sm"
                                            class="text-[#00FFF7] hover:text-[#00FFF7]/80 border-[#00FFF7]/30 hover:border-[#00FFF7]/50">
                                            <flux:icon name="eye" class="h-4 w-4 mr-1" />
                                            Ver Perfil
                                        </flux:button>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <!-- Empty State -->
                    <div class="px-6 py-12 text-center">
                        <div class="mx-auto w-24 h-24 bg-zinc-700 rounded-full flex items-center justify-center mb-4">
                            <flux:icon name="user-plus" class="h-12 w-12 text-gray-400" />
                        </div>
                        <h3 class="text-lg font-medium text-gray-300 mb-2">
                            Nenhuma solicitação pendente
                        </h3>
                        <p class="text-gray-500 max-w-md mx-auto">
                            Quando alguém solicitar para seguir você, as solicitações aparecerão aqui para você aceitar ou rejeitar.
                        </p>
                        <div class="mt-6">
                            <flux:button 
                                onclick="window.location.href='/busca'"
                                variant="primary"
                                class="bg-[#E60073] hover:bg-[#E60073]/80">
                                <flux:icon name="magnifying-glass" class="h-4 w-4 mr-2" />
                                Encontrar Pessoas
                            </flux:button>
                        </div>
                    </div>
                @endif
            </div>

            <!-- Help Section -->
            @if(count($followRequests) > 0)
                <div class="mt-6 bg-zinc-800/50 border border-zinc-700 rounded-lg p-4">
                    <div class="flex items-start gap-3">
                        <flux:icon name="information-circle" class="h-5 w-5 text-[#00FFF7] mt-0.5 flex-shrink-0" />
                        <div>
                            <h4 class="text-sm font-medium text-gray-300 mb-1">Dicas sobre solicitações</h4>
                            <ul class="text-sm text-gray-400 space-y-1">
                                <li>• Aceitar uma solicitação permite que a pessoa veja seus posts</li>
                                <li>• Você pode visitar o perfil antes de decidir</li>
                                <li>• Solicitações rejeitadas não notificam o usuário</li>
                            </ul>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
</flux:main>
